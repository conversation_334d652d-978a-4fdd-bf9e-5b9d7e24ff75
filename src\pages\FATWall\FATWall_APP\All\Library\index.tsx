import baseStyles from "@/pages/FATWall/FATWall_APP/index.module.scss";
import { useHistory, useRouteMatch, useLocation } from "react-router-dom";
import NavigatorBar from "@/components/NavBar";
import { IListCard } from "@/components/ListCard";
import FilmFilter from "../../../../../components/FATWall_APP/FilmFilter";
import Pull2Refresh from "@/components/Pull2Refresh";
import FilterFilmCard from "../../../../../components/FATWall_APP/FilterFilmCard";
import FloatPanel from "@/components/FloatPanel";
import { PreloadImage } from "@/components/Image";
import { CheckList, Popover } from "antd-mobile";
import styles from '../index.module.scss';
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { CheckListValue } from "antd-mobile/es/components/check-list";
import { useTheme } from "@/utils/themeDetector";

import close from '@/Resources/icon/close.png';
import close_dark from '@/Resources/icon/close_white.png';
import moreIcon from "@/Resources/filmWall/more.png";
import moreIcon_dark from "@/Resources/filmWall/more_dark.png";
import filterIcon from "@/Resources/filmWall/filter.png";
import filterIcon_dark from "@/Resources/filmWall/filter_dark.png";
import { defaultFiltersByApp, filterItemType, filterTypeList } from "@/components/FATWall_APP/FATWALL_CONST";
import { mediaProps, getMediaListFromLib } from "@/api/fatWall";
import { handleFilter } from "..";
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import FATErrorComponents from "@/pages/FATWall/FATWall_PC/Error";
import { defaultPageParam } from "../../Recently";

const FATLibrary = () => {
  const [collapse, setCollapse] = useState<boolean>(true);
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByApp);
  const { isDarkMode } = useTheme();
  const [showFloatPanel, setShowFloatPanel] = useState(false); // 控制浮动面板
  const containerRef = useRef<HTMLDivElement | null>(null); // 滚动容器
  const [moreVisible, setMoreVisible] = useState<boolean>(false);
  const history = useHistory<IListCard & { lib_id: number; fromLibrary?: boolean; libraryTitle?: string; classes?: string; media_id?: number }>();
  const location = useLocation();
  const routeMatch = useRouteMatch();
  const prefix = routeMatch.path.split('/')[1];

  // 从URL参数和state中获取数据，优先使用URL参数
  const getPageParams = useCallback(() => {
    const urlParams = new URLSearchParams(location.search);
    const state = history.location.state;

    return {
      lib_id: urlParams.get('lib_id') ? parseInt(urlParams.get('lib_id')!) : state?.lib_id,
      title: urlParams.get('title') || state?.title,
      fromLibrary: urlParams.get('fromLibrary') === 'true' || state?.fromLibrary,
      libraryTitle: urlParams.get('libraryTitle') || state?.libraryTitle,
      classes: urlParams.get('classes') || state?.classes,
      media_id: urlParams.get('media_id') ? parseInt(urlParams.get('media_id')!) : state?.media_id
    };
  }, [location.search, history.location.state]);

  const pageParams = getPageParams();

  // 添加详细的参数日志打印
  useEffect(() => {
    console.log('=== FATLibrary 页面参数调试信息 ===');
    console.log('URL search params:', location.search);
    console.log('history.location.state:', JSON.stringify(history.location.state, null, 2));
    console.log('合并后的pageParams:', JSON.stringify(pageParams, null, 2));
    console.log('routeMatch:', JSON.stringify(routeMatch, null, 2));
    console.log('prefix:', JSON.stringify(prefix, null, 2));
    console.log('pageParams.lib_id是否存在:', !!pageParams.lib_id);
    if (!pageParams.lib_id) {
      console.log('⚠️ lib_id 为空，这可能是导致页面报错的原因');
    }
    console.log('=== FATLibrary 参数调试信息结束 ===');
  }, [location.search, history.location.state, pageParams, routeMatch, prefix]);
  const [medias, setMedias] = useState<mediaProps[]>([]);
  const [filterItem, setFilterItem] = useState<filterItemType>({ sort_type: 0, asc: 0 });
  const filterItemRef = useRef<filterItemType>({ sort_type: 0, asc: 0 });
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByApp); // 记录之前的筛选条件，用于判断筛选条件是否变化

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const { runAsync: getMediasFromLib } = useRequest(getMediaListFromLib, { manual: true }); // 根据媒体库获取所有资源
  const [isError, setIsError] = useState<boolean>(false); // 是否出现错误信息

  // 媒体库数据
  const filterFilm = useMemo(() => {
    console.log('=== filterFilm 计算 ===');
    console.log('pageParams:', JSON.stringify(pageParams, null, 2));
    console.log('medias数组长度:', medias?.length || 0);
    console.log('medias前3项:', JSON.stringify(medias?.slice(0, 3), null, 2));

    if (!pageParams || !pageParams.lib_id) {
      console.log('⚠️ pageParams或pageParams.lib_id为空，返回空数组');
      return [];
    }

    const result = medias.map((item) => {
      return { label: item.trans_name, score: item.score || 0, time: `${item.year}`, cover: item.poster.length > 0 ? item.poster[0] : '', isLike: item.favourite, lib_id: pageParams.lib_id, media_id: item.media_id, classes: item.classes }
    });

    console.log('filterFilm计算结果长度:', result.length);
    console.log('filterFilm前3项:', JSON.stringify(result.slice(0, 3), null, 2));

    return result;
  }, [medias, pageParams])

  const initMediaInfo = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    console.log('=== initMediaInfo 调用 ===');
    console.log('pageParams:', JSON.stringify(pageParams, null, 2));
    console.log('filter参数:', JSON.stringify(filter, null, 2));
    console.log('pageOptRef.current:', JSON.stringify(pageOptRef.current, null, 2));
    console.log('filterItemRef.current:', JSON.stringify(filterItemRef.current, null, 2));

    if (!pageParams) {
      console.log('⚠️ pageParams为空，initMediaInfo提前返回');
      return;
    }

    if (!pageParams.lib_id) {
      console.log('⚠️ pageParams.lib_id为空，值为:', JSON.stringify(pageParams.lib_id, null, 2));
      return;
    }

    const requestParams = { lib_id: pageParams.lib_id, filter: { ...pageOptRef.current, ...filterItemRef.current, ...filter } };
    console.log('API请求参数:', JSON.stringify(requestParams, null, 2));

    const mediaRes = await getMediasFromLib(requestParams).catch((e) => {
      console.log('获取媒体库影视列表失败：', e);
      console.log('错误详情:', JSON.stringify(e, null, 2));
      return null;
    });

    console.log('API响应结果:', JSON.stringify(mediaRes, null, 2));

    if (mediaRes && mediaRes.code === 0 && mediaRes.data) {
      console.log('API调用成功，数据条数:', mediaRes.data.medias?.length || 0);
      if (mediaRes.data.count < pageOptRef.current.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      callback(mediaRes.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      return;
    }
    console.log('⚠️ API调用失败或返回数据异常，设置错误状态');
    setIsError(true);
  }, [getMediasFromLib, pageParams])

  // 初始化数据
  useEffect(() => {
    console.log('=== 初始化数据 useEffect 触发 ===');
    try {
      console.log('defaultFiltersByApp:', JSON.stringify(defaultFiltersByApp, null, 2));
      const processedFilter = handleFilter(defaultFiltersByApp);
      console.log('处理后的filter:', JSON.stringify(processedFilter, null, 2));
      initMediaInfo((data) => setMedias(data), processedFilter);
    } catch (error) {
      console.error('⚠️ 初始化数据时发生错误:', error);
      console.error('错误详情:', JSON.stringify(error, null, 2));
    }
  }, [initMediaInfo])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      initMediaInfo((data) => setMedias(p => [...p, ...data]), handleFilter(filters));
    }
  }, [inViewport, initMediaInfo])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilter(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(false);
    pageOptRef.current = defaultPageParam; // 重置页数
    initMediaInfo((data) => setMedias(data), filter);
  }, [initMediaInfo])

  useUpdateEffect(() => {
    filterItemRef.current = { ...filterItemRef.current, sort_type: filterItem.sort_type, asc: filterItem.asc }; // 记录之前的筛选条件，用于判断筛选条件是否变化
    initMediaInfo((data) => setMedias(data), handleFilter(filters));
  }, [filterItem])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    getDataByFilters(filters); // 筛选条件更新的时候，重置页数，重置是否还有更多数据
  }, [filters, getDataByFilters])

  const clearAndRefresh = useCallback(() => {
    setFilters(defaultFiltersByApp); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByApp)) {
        getDataByFilters(defaultFiltersByApp);
      }
    }
  }, [getDataByFilters])

  const checkListOnchange = useCallback((val: CheckListValue[], type: 'asc' | 'sort_type') => {
    setFilterItem(prev => {
      let p: filterItemType | any = { ...prev };
      p[type] = val[0];
      return p;
    })

    setShowFloatPanel(false); // 关闭筛选面板
  }, [setShowFloatPanel])

  const toLibraryManagement = useCallback(() => {
    if (!pageParams) return;
    history.push({
      pathname: `/${prefix}/libraryManagement`,
      state: {
        lib_id: pageParams?.lib_id,
        title: pageParams?.title,
        fromLibrary: true
      } as any
    });
  }, [history, prefix, pageParams]);

  const rightSize = useMemo(() => {
    return (
      <div className={styles.right}>
        {/* <PreloadImage src={isDarkMode ? searchIcon_dark : searchIcon} alt="search" /> */}
        <PreloadImage src={isDarkMode ? filterIcon_dark : filterIcon} alt="filter" onClick={() => setShowFloatPanel(true)} />
        <Popover
          className={styles.morePopoverContainer}
          visible={moreVisible}
          onVisibleChange={setMoreVisible}
          content={
            <div className={styles.morePopover}>
              <div className={styles.morePopoverItem} onClick={toLibraryManagement}>
                <span className={styles.morePopoverText}>媒体库管理</span>
              </div>
            </div>
          }
          trigger='click'
          placement='bottom-end'
          style={{ '--arrow-size': '0px' } as React.CSSProperties}
        >
          <PreloadImage src={isDarkMode ? moreIcon_dark : moreIcon} alt="more" onClick={() => setMoreVisible(true)} />
        </Popover>
      </div>
    )
  }, [isDarkMode, moreVisible, toLibraryManagement])

  const toDarmaOrMovie = useCallback((item: any) => {
    if (!pageParams) return;
    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: pageParams.lib_id?.toString() || '0',
      title: pageParams.title || ''
    });

    history.push({
      pathname: `/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`,
      state: {
        ...pageParams, // 保留原有的参数
        fromLibrary: true,
        libraryTitle: pageParams.title,
        classes: item.classes,
        media_id: item.media_id
      }
    });
  }, [history, pageParams])

  useEffect(() => {
    // if (isError || medias.length === 0) {
    //   setCollapse(true); // 如果没有影片数据，则默认收起筛选条件
    // }

    // if (medias.length > 0 && pageOptRef.current.offset === 0) {
    //   setCollapse(false); // 如果有影片数据，则默认展开筛选条件
    // }
  }, [isError, medias.length])


  useEffect(() => {
    // 当筛选条件展开后,滚动到顶部
    if (!collapse && containerRef && containerRef.current) {
      containerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [collapse])

  // 页面渲染前的最终检查
  console.log('=== 页面渲染前检查 ===');

  console.log('pageParams存在性:', !!pageParams);
  console.log('pageParams.lib_id:', pageParams?.lib_id);
  console.log('filterFilm长度:', filterFilm?.length || 0);
  console.log('isError状态:', isError);
  console.log('hasMore状态:', hasMore);

  if (!pageParams || !pageParams.lib_id) {
    console.log('⚠️ pageParams或lib_id为空，跳转到首页');
    history.push('filmAndTelevisionWall_app');
    return <></>;
  };

  console.log('✅ 页面正常渲染');
  return (
    <>
      <NavigatorBar onBack={() => history.push({
        pathname: `/filmAndTelevisionWall_app/all`
      })} right={rightSize} backIconTheme={isDarkMode ? 'dark' : 'light'} />
      <div className={baseStyles.container_subPage}>
        <div className={baseStyles.tabsHeader}>
          <span className={`${baseStyles.tabsHeader_span} ${baseStyles.path_active}`}>{pageParams.title}</span>
        </div>

        <div className={styles.container} ref={containerRef}>
          {/* 条件筛选 */}
          {(!isError) && <FilmFilter filters={filters} filterTypeList={filterTypeList} controlFilters={setFilters} value={collapse} onChange={setCollapse} />
          }

          {/* 下拉刷新组件 */}
          <Pull2Refresh style={{ height: `${filterFilm.length === 0 ? `calc(100% - ${collapse ? '40px' : '320px'})` : ''}` }} onRefresh={clearAndRefresh} isTrigger={collapse} containerRef={containerRef}>
            <FATErrorComponents refresh={clearAndRefresh} show={isError || filterFilm.length === 0} span={isError ? '获取失败' : '暂无内容'} canTry={isError} subSpan={filterFilm.length === 0 ? '请在媒体库关联的文件夹中添加视频' : undefined}>
              {/* 影片渲染 */}
              <div className={styles.filter_films_container}>
                {
                  filterFilm.map((item, index) => (
                    <FilterFilmCard type='app' key={item.label + index} title={item.label} subtitle={item.time} score={item.score} cover={item.cover} isLike={item.isLike ? true : false} onCardClick={() => toDarmaOrMovie(item)} />
                  ))
                }
              </div>
            </FATErrorComponents>
          </Pull2Refresh>

          {
            hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
          }

          {/* 弹出浮动面板 */}
          <FloatPanel showFloatPanel={showFloatPanel} setShowFloatPanel={setShowFloatPanel}>
            <div className={styles.filter_float_panel_container}>
              <div className={styles.filter_float_panel_navBar}>
                <PreloadImage src={isDarkMode ? close_dark : close} alt='close' />
                <span>筛选</span>
              </div>
              <div className={styles.filter_float_panel_content}>
                <span className={styles.filter_float_panel_content_list_title}>筛选</span>
                <div className={styles.filter_float_panel_content_check_list_container}>
                  <CheckList value={[filterItem.sort_type]} onChange={(v) => checkListOnchange(v, 'sort_type')}>
                    <CheckList.Item style={{ color: filterItem.sort_type === 0 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={0}>添加时间</CheckList.Item>
                    <CheckList.Item style={{ color: filterItem.sort_type === 1 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={1}>评分</CheckList.Item>
                  </CheckList>
                </div>
                <span className={styles.filter_float_panel_content_list_title}>筛选</span>
                <div className={styles.filter_float_panel_content_check_list_container}>
                  <CheckList value={[filterItem.asc]} onChange={(v) => checkListOnchange(v, 'asc')}>
                    <CheckList.Item style={{ color: filterItem.asc === 0 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={0}>正序</CheckList.Item>
                    <CheckList.Item style={{ color: filterItem.asc === 1 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={1}>倒序</CheckList.Item>
                  </CheckList>
                </div>
              </div>
            </div>
          </FloatPanel>
        </div>
      </div >
    </>
  )
}

export default FATLibrary;