.root_container,
.root_subContainer {
  width: 100%;
  height: calc(100% - 35px);
  padding-top: 35px;
  background-color: var(--background-color);
}

.root_subContainer {
  height: 100%;
}

.right {
  img {
    height: 26px;
    margin: 0 10px;
  }
}

.loadingContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container,
.container_subPage {
  width: 100%;
  height: calc(100% - 80px);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整个容器滚动 */
}

.container_subPage {
  height: calc(100% - 20px);
}

.tabsHeader {
  padding: 10px 26px;
  margin-bottom: 12px;
}
.tabsHeader_span {
  font-family: MiSans;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: var(--list-value-text-color);
  margin-right: 20px;
}

.path_active {
  color: var(--text-color) !important;
}

// 搜索界面
.matchCorrectionOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.searchContainer {
  padding: 14px 16px;
  padding-top: 35px;
  display: flex;
  align-items: center;
  background-color: var(--background-color);
  flex-shrink: 0; /* 防止搜索栏被压缩 */
}

.searchInputWrapper {
  flex: 1;
  background-color: var(--cancel-btn-background-color);
  border-radius: 16px;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
}

.searchIconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2px 0 10px;
}

.searchIcon {
  font-size: 18px;
  color: var(--list-value-text-color);
}

.searchInput {
  --font-size: 16px;
  --color: var(--text-color);
  --placeholder-color: var(--thinLine-background-color);
  height: 36px;
  flex: 1;

  :global {
    .adm-input {
      background-color: transparent;
    }

    .adm-input-element {
      font-size: 16px;
      padding-left: 4px;
    }
  }
}

.cancelButton {
  padding: 0 12px;
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
  cursor: pointer;
  white-space: nowrap;
}

.searchResults {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  -webkit-overflow-scrolling: touch; // 为iOS提供平滑滚动
  background-color: var(--background-color);
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

/* 搜索状态容器 */
.searchStateContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: 20px;
}

/* 加载状态 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 16px;
  color: var(--text-color);
}

/* 空状态 */
.errorBlock {
  margin-bottom: 16px;
}

.retryButton {
  margin-top: 16px;
  font-size: 16px;
}

/* 固定的分类标签栏样式 */
.search_category_tabs_fixed {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-color);
  display: flex;
  justify-content: flex-start;
  gap: 8px;
  padding: 16px;
  margin-bottom: 8px;
  min-height: 60px; /* 确保有足够的高度 */
  flex-shrink: 0; /* 防止被压缩 */

  .category_tab {
    background-color: var(--search-tab-bg);
    // width: 65px;
    height: 29px;
    flex: 0 0 auto;
    line-height: 29px;
    text-align: center;
    padding: 0px 11px;
    font-size: 14px;
    color: var(--text-color);
    border-radius: 12px;
    border: 1px solid transparent; /* 添加边框确保稳定性 */

    &:hover {
      opacity: 0.8;
    }

    &.active_tab {
      background-color: var(--search-tab-active-bg);
      color: #3482ff;
      font-weight: 600;
    }
  }
}

/* 可滚动的搜索结果区域 */
.search_results_scrollable {
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 180px); /* 减去顶部固定区域的高度 */
}

.filter_films_container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 0 8px;
  padding-bottom: 80px;
  justify-items: center;
}

.morePopoverContainer {
  :global(.adm-popover-inner) {
    border-radius: 15px !important;
  }
}

.morePopover {
  min-width: 160px;
}

.morePopoverItem {
  padding: 10px 8px;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #000000;
  transition: background-color 0.2s;

  &:hover,
  &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:first-child {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
  }

  &:last-child {
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
  }
}

.morePopoverText {
  font-size: 16px;
  color: var(--text-color);
}
