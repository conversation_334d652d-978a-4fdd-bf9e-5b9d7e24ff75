.nasTV_optPages_container {
  width: 100%;
  height: 100%;
  background: rgba(57, 65, 77, 1);
  color: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  scroll-behavior: smooth;
  padding: 0 68px;
  user-select: none;
}

.nasTV_optPages_content {
  width: 100%;
  height: 440px;
  display: flex;
  flex-wrap: wrap;
  // gap: 20px;
  padding: 0 40px;

  > div {
    margin-right: 20px;
    margin-bottom: 20px;
  }
}

.nasTV_index_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
  color: var(--background-color);
  position: relative;
}

.nasTV_index_desc {
  width: 100%;
  padding: 100px 75px 20px 75px;
  display: flex;
  flex-direction: column;
  // gap: 20px;
  position: relative;

  > div {
    margin-bottom: 20px;
  }
}

.nasTV_index_desc_flag_container {
  display: flex;
  align-items: center;
  width: 100%;
  > div {
    margin-right: 10px;
  }
  // gap: 10px;
}

.nasTV_index_desc_flag_add_item {
  padding: 2px 8px;
  color: #fff;
  font-family: MiSans;
  font-size: 30px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nasTV_index_desc_flag_score,
.nasTV_index_desc_flag_add_container {
  height: 48px;
  display: flex;
  align-items: center;
  // gap: 10px;
  > div {
    margin-right: 10px;
  }
}

.nasTV_index_desc_flag_score {
  justify-content: center;

  .nasTV_index_desc_flag_add_item {
    padding: 2px 0;
    min-width: 60px;
    height: 36px;
  }
}

.nasTV_index_desc_flag_score_item {
  display: flex;
  width: 100%;
  height: 100%;
  color: #07b551;
  font-family: MiSans;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: -1px;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  border: 2px solid #07b551;
  // gap: 6px;
  align-items: center;
  justify-content: center;

  >img {
    margin-right: 6px;
  }

  img {
    width: 22px;
    height: 22px;
  }
}

.nasTV_index_desc_flag_gap {
  line-height: 100%;
  width: 5px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4px;

  div {
    width: 1px;
    height: 12px;
    rotate: 15deg;
    background-color: #fff;
  }
}

.nasTV_index_desc_flag {
  padding: 2px 8px;
  font-size: 24px;
  font-weight: 400;
  border: 1px solid white;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nasTV_index_desc_title {
  width: 100%;
  height: 133px;
  font-size: 100px;
  font-weight: 400;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.nasTV_index_desc_describe {
  width: 980px;
  height: 180px;
  font-size: 24px;
  font-weight: 400;
  line-clamp: 4;
  overflow: hidden;
  box-sizing: border-box;
  line-height: 1.5; /* 设置行高，确保文字不会半截显示 */
  white-space: wrap;
  word-wrap: break-word;
}

.nasTV_index_desc_recentlyWatched {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0 68px;
  transition: all 0.2s ease-in;
}

.nasTV_index_desc_recentlyWatched_text {
  width: 152px;
  height: 50px;
  font-size: 38px;
  font-weight: 700;
}

.nasTV_index_desc_recentlyWatched_list {
  width: 100%;
  height: 100%;
  display: inline-flex;
  align-items: center;
  // gap: 40px;
  user-select: none;
  overflow-x: auto;
  scrollbar-width: none;
  scroll-behavior: smooth;

  > div {
    margin-right: 40px;
  }
}

.nasTV_index_desc_recentlyWatched_list_item {
  width: 395px;
  display: flex;
  flex-direction: column;
  padding: 10px 0;

  img {
    width: 385px;
    min-width: 385px;
    height: 220px;
    border-radius: 16px;
  }
}

.nasTV_index_desc_recentlyWatched_list_item_cover {
  width: 395px;
  height: 230px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.nasTV_index_desc_recentlyWatched_list_item_cover_item {
  position: relative;
  height: 220px;

  img {
    height: 100%;
    object-fit: cover;
  }
}

.nasTV_index_desc_recentlyWatched_list_item_cover_item_tempImg {
  width: 385px;
  height: 220px;
  border-radius: 16px;
  background-color: rgba(140, 147, 176, 0.1);
}

.nasTV_index_desc_recentlyWatched_list_item_cover:focus {
  border: 4px solid #fff;
  transform: scale(1) !important;
  border-radius: 16px;

  img {
    border-radius: 16px;
  }
}

.nasTV_index_desc_recentlyWatched_list_item_titleSpan,
.nasTV_index_desc_recentlyWatched_list_item_continueTitle {
  font-family: MiSans W;
  font-weight: 400;
  font-size: 22px;
  line-height: 100%;
  letter-spacing: 0px;
  padding: 2px 10px;
  margin-top: 10px;
}

.nasTV_index_desc_recentlyWatched_list_item_titleSpan {
  color: var(--background-color);
}

.nasTV_index_desc_recentlyWatched_list_item_continueTitle {
  opacity: 50%;
  color: var(--card-hover-color);
}

.nasTV_index_desc_recentlyWatched_list_item_progress {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  border-radius: 16px;

  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 96%,
    rgba(255, 255, 255, 0.2) 97%,
    rgba(255, 255, 255, 0.2) 100%
  );
}

.nasTV_index_desc_recentlyWatched_list_item_progressBar {
  content: "";
  display: block;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 96%,
    rgba(89, 156, 250, 1) 97%,
    rgba(89, 156, 250, 1) 100%
  );
  transition: all 0.3s;
  border-radius: 16px;
}

.nasTV_index_desc_btns {
  display: flex;
  // gap: 20px;
  margin-top: 28px;
  padding: 10px 0;
  > div {
    margin-right: 20px;
  }
}

.nasTV_index_btns_play_item {
  width: 270px;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-color);
  gap: 15px;
  border-radius: 12px;

  color: #fff;
  text-align: center;
  font-family: MiSans;
  font-size: 33px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;

  img {
    height: 39px;
  }
}

.nasTV_index_btns_item {
  width: 140px;
  height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  gap: 15px;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  text-align: center;
  font-family: MiSans;
  font-size: 33px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;

  img {
    height: 50px;
  }
}

.nasTV_index_desc_recentlyWatched_more_item_container {
  width: 240px;
  height: 310px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 10px;
}

.nasTV_index_desc_recentlyWatched_more_item_focus_container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 220px;
}

.nasTV_index_desc_recentlyWatched_more_item {
  width: 240px;
  height: 220px;
  background-color: var(--list-value-text-color-reverse);
  font-family: MiSans W;
  font-weight: 400;
  font-size: 22px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.nasTV_index_desc_recentlyWatched_more_item_focus_container:focus {
  border: 4px solid #fff;
  transform: scale(1) !important;
  border-radius: 16px;

  .nasTV_index_desc_recentlyWatched_more_item {
    border-radius: 8px;
  }
}

.nasTV_index_desc_describe_more {
  width: 65px;
  height: 40px;
  position: absolute;
  bottom: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  font-size: 24px;
  margin: 0 !important;
}

.nasTV_mask {
  width: 100%;
  height: 100%;
  position: absolute;
  background: linear-gradient(
      172deg,
      rgba(30, 70, 106, 0) 22.2%,
      #c68c8c69 99.9%
    ),
    radial-gradient(
      101.78% 92.28% at 79.9% 20.6%,
      rgba(8, 56, 75, 0) 0%,
      rgba(2, 21, 29, 0.59) 65.22%,
      rgba(0, 6, 10, 0.85) 100%
    );
  background-blend-mode: multiply, normal;
}

.nasTV_background_image {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1;
  object-fit: cover;
}
