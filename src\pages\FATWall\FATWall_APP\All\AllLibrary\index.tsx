import NavigatorBar from "@/components/NavBar"
import { useHistory, useRouteMatch } from "react-router-dom";
import baseStyles from "@/pages/FATWall/FATWall_APP/index.module.scss";
import styles from "./index.module.scss";
import { useCallback, useEffect, useState } from "react";
import ListCard, { IListCard } from "@/components/ListCard";
import { useRequest } from 'ahooks';
import { getFullLibraryData, MyLibProps, SharedLibProps } from "@/api/fatWall";
import cover from '@/Resources/dashboardCard/Frame.png';

const AllLibrary = () => {
  const history = useHistory();
  const routeMatch = useRouteMatch();
  const [myselfCreate, setMyselfCreate] = useState<(IListCard & MyLibProps)[]>([]); // 自己创建的媒体库
  const [otherShare, setOtherShare] = useState<(IListCard & SharedLibProps)[]>([]); // 别人分享的媒体库
  const path = routeMatch.path.split('/')[1];
  const { runAsync: getLibraryData } = useRequest(getFullLibraryData, { manual: true, }); // 获取所有媒体库

  const initLib = useCallback(async () => {
    const res = await getLibraryData({ sort_type: 0, asc: 0 }).catch(e => console.log('获取媒体库列表失败:', e));
    if (res && res.code === 0 && res.data) {
      const myself: (IListCard & MyLibProps)[] = res.data.my_libs.libs.map(item => {
        return {
          avatar: item.poster && item.poster.length > 0 ? item.poster[0] : cover,
          title: item.name,
          subtitle: `最近更新 ${new Date(Number(item.update_time || item.create_time) * 1000).toLocaleDateString()}`,
          type: 'arrow',
          ...item
        }
      })
      const others: (IListCard & SharedLibProps)[] = res.data.share2me.libs.map(item => {
        return {
          avatar: item.poster && item.poster.length > 0 ? item.poster[0] : cover,
          title: item.name,
          subtitle: `最近更新 ${new Date(Number(item.update_time || item.create_time) * 1000).toLocaleDateString()}`,
          type: 'arrow',
          ...item
        }
      })

      setMyselfCreate(myself);
      setOtherShare(others);
    }
  }, [getLibraryData])

  // 初始化媒体库信息
  useEffect(() => {
    initLib()
  }, [initLib])

  return (
    <>
      <NavigatorBar onBack={() => history.goBack()} />
      <div className={styles.container}>
        <div className={`${baseStyles.tabsHeader_span} ${baseStyles.path_active}`}>
          <span className={baseStyles.path_active}>全部媒体库</span>
        </div>
        <div className={styles.content}>
          <div className={styles.library_content_container}>
            <span className={styles.library_title}>{`我创建的(${myselfCreate.length}个)`}</span>
            {
              myselfCreate.map((item) => (<ListCard key={item.name + item.lib_id} {...item}
                onCallback={() => {
                  // 将参数放到URL中，以便安卓goback时能正确获取
                  const params = new URLSearchParams({
                    lib_id: item.lib_id?.toString() || '0',
                    title: item.title || item.name || '',
                    fromLibrary: 'false'
                  });
                  history.push(`/${path}/library?${params.toString()}`);
                }} />))
            }
          </div>
          <div className={styles.library_content_container}>
            <span className={styles.library_title}>{`别人分享给我的(${otherShare.length}个)`}</span>
            {
              otherShare.map((item) => (<ListCard key={item.name + item.lib_id} {...item}
                onCallback={() => {
                  // 将参数放到URL中，以便安卓goback时能正确获取
                  const params = new URLSearchParams({
                    lib_id: item.lib_id?.toString() || '0',
                    title: item.title || item.name || '',
                    fromLibrary: 'false'
                  });
                  history.push(`/${path}/library?${params.toString()}`);
                }} />))
            }
          </div>
        </div>
      </div>
    </>
  )
}

export default AllLibrary;